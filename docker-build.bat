@echo off
chcp 65001 >nul
setlocal

REM NGAP Engine Docker构建脚本 (Windows版本)

echo 开始构建NGAP Engine Docker镜像...

REM 设置镜像名称和标签
set IMAGE_NAME=ngap-engine
set IMAGE_TAG=latest
set FULL_IMAGE_NAME=%IMAGE_NAME%:%IMAGE_TAG%

REM 构建Docker镜像
echo 构建镜像: %FULL_IMAGE_NAME%
docker build -t %FULL_IMAGE_NAME% .

REM 检查构建结果
if %ERRORLEVEL% equ 0 (
    echo ✅ Docker镜像构建成功!
    echo 镜像名称: %FULL_IMAGE_NAME%
    
    REM 显示镜像信息
    echo.
    echo 镜像详情:
    docker images %IMAGE_NAME%
    
    echo.
    echo 🚀 使用以下命令运行容器:
    echo    docker-run.bat
    echo    或者:
    echo    docker run -p 5000:5000 %FULL_IMAGE_NAME%
    echo.
    echo 💡 使用Docker Compose:
    echo    docker-compose up -d
) else (
    echo ❌ Docker镜像构建失败!
    pause
    exit /b 1
)

pause
