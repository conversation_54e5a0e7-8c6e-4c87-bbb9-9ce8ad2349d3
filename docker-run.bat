@echo off
chcp 65001 >nul
setlocal

REM NGAP Engine Docker运行脚本 (Windows版本)

echo 启动NGAP Engine容器...

REM 设置镜像名称和标签
set IMAGE_NAME=ngap-engine
set IMAGE_TAG=latest
set FULL_IMAGE_NAME=%IMAGE_NAME%:%IMAGE_TAG%
set CONTAINER_NAME=ngap-engine-container

REM 检查镜像是否存在
docker images %IMAGE_NAME% | findstr %IMAGE_TAG% >nul
if %ERRORLEVEL% neq 0 (
    echo ❌ 镜像 %FULL_IMAGE_NAME% 不存在!
    echo 请先运行构建脚本: docker-build.bat
    pause
    exit /b 1
)

REM 停止并删除已存在的容器
docker ps -a | findstr %CONTAINER_NAME% >nul
if %ERRORLEVEL% equ 0 (
    echo 停止并删除已存在的容器...
    docker stop %CONTAINER_NAME% >nul 2>&1
    docker rm %CONTAINER_NAME% >nul 2>&1
)

REM 运行新容器
echo 启动新容器: %CONTAINER_NAME%
docker run -d --name %CONTAINER_NAME% -p 5000:5000 --restart unless-stopped %FULL_IMAGE_NAME%

REM 检查容器状态
if %ERRORLEVEL% equ 0 (
    echo ✅ 容器启动成功!
    echo.
    echo 容器信息:
    docker ps | findstr %CONTAINER_NAME%
    echo.
    echo 🌐 API服务地址: http://localhost:5000
    echo 🏥 健康检查: http://localhost:5000/health
    echo.
    echo 📋 常用命令:
    echo    查看日志: docker logs %CONTAINER_NAME%
    echo    停止容器: docker stop %CONTAINER_NAME%
    echo    进入容器: docker exec -it %CONTAINER_NAME% /bin/bash
    echo.
    echo 💡 使用Docker Compose管理:
    echo    启动: docker-compose up -d
    echo    停止: docker-compose down
) else (
    echo ❌ 容器启动失败!
    pause
    exit /b 1
)

pause
